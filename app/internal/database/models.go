// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package database

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

type Account struct {
	ID          uuid.UUID `json:"id"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	AccountType string    `json:"account_type"`
	IsActive    bool      `json:"is_active"`
}

type ClientSecret struct {
	HashedSecret            string     `json:"hashed_secret"`
	CreatedAt               time.Time  `json:"created_at"`
	UpdatedAt               time.Time  `json:"updated_at"`
	ServiceAccountAccountID uuid.UUID  `json:"service_account_account_id"`
	ExpiresAt               time.Time  `json:"expires_at"`
	RevokedAt               *time.Time `json:"revoked_at"`
}

type Isn struct {
	ID            uuid.UUID `json:"id"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	UserAccountID uuid.UUID `json:"user_account_id"`
	Title         string    `json:"title"`
	Slug          string    `json:"slug"`
	Detail        string    `json:"detail"`
	IsInUse       bool      `json:"is_in_use"`
	Visibility    string    `json:"visibility"`
}

type IsnAccount struct {
	ID         uuid.UUID `json:"id"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
	IsnID      uuid.UUID `json:"isn_id"`
	AccountID  uuid.UUID `json:"account_id"`
	Permission string    `json:"permission"`
}

type OneTimeClientSecret struct {
	ID                      uuid.UUID `json:"id"`
	CreatedAt               time.Time `json:"created_at"`
	ServiceAccountAccountID uuid.UUID `json:"service_account_account_id"`
	PlaintextSecret         string    `json:"plaintext_secret"`
	ExpiresAt               time.Time `json:"expires_at"`
}

type RefreshToken struct {
	HashedToken   string     `json:"hashed_token"`
	UserAccountID uuid.UUID  `json:"user_account_id"`
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
	ExpiresAt     time.Time  `json:"expires_at"`
	RevokedAt     *time.Time `json:"revoked_at"`
}

type ServiceAccount struct {
	AccountID          uuid.UUID `json:"account_id"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
	ClientID           string    `json:"client_id"`
	ClientContactEmail string    `json:"client_contact_email"`
	ClientOrganization string    `json:"client_organization"`
}

type Signal struct {
	ID            uuid.UUID `json:"id"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	AccountID     uuid.UUID `json:"account_id"`
	IsnID         uuid.UUID `json:"isn_id"`
	SignalTypeID  uuid.UUID `json:"signal_type_id"`
	LocalRef      string    `json:"local_ref"`
	CorrelationID uuid.UUID `json:"correlation_id"`
	IsWithdrawn   bool      `json:"is_withdrawn"`
	IsArchived    bool      `json:"is_archived"`
}

type SignalBatch struct {
	ID        uuid.UUID `json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	IsnID     uuid.UUID `json:"isn_id"`
	AccountID uuid.UUID `json:"account_id"`
	IsLatest  bool      `json:"is_latest"`
}

type SignalProcessingFailure struct {
	ID               uuid.UUID `json:"id"`
	CreatedAt        time.Time `json:"created_at"`
	SignalBatchID    uuid.UUID `json:"signal_batch_id"`
	SignalTypeSlug   string    `json:"signal_type_slug"`
	SignalTypeSemVer string    `json:"signal_type_sem_ver"`
	LocalRef         string    `json:"local_ref"`
	ErrorCode        string    `json:"error_code"`
	ErrorMessage     string    `json:"error_message"`
}

type SignalType struct {
	ID            uuid.UUID `json:"id"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	IsnID         uuid.UUID `json:"isn_id"`
	Slug          string    `json:"slug"`
	SchemaURL     string    `json:"schema_url"`
	ReadmeURL     string    `json:"readme_url"`
	Title         string    `json:"title"`
	Detail        string    `json:"detail"`
	SemVer        string    `json:"sem_ver"`
	IsInUse       bool      `json:"is_in_use"`
	SchemaContent string    `json:"schema_content"`
}

type SignalVersion struct {
	ID            uuid.UUID       `json:"id"`
	CreatedAt     time.Time       `json:"created_at"`
	AccountID     uuid.UUID       `json:"account_id"`
	SignalBatchID uuid.UUID       `json:"signal_batch_id"`
	SignalID      uuid.UUID       `json:"signal_id"`
	VersionNumber int32           `json:"version_number"`
	Content       json.RawMessage `json:"content"`
}

type SignalVersionsP0 struct {
	ID            uuid.UUID       `json:"id"`
	CreatedAt     time.Time       `json:"created_at"`
	AccountID     uuid.UUID       `json:"account_id"`
	SignalBatchID uuid.UUID       `json:"signal_batch_id"`
	SignalID      uuid.UUID       `json:"signal_id"`
	VersionNumber int32           `json:"version_number"`
	Content       json.RawMessage `json:"content"`
}

type SignalVersionsP1 struct {
	ID            uuid.UUID       `json:"id"`
	CreatedAt     time.Time       `json:"created_at"`
	AccountID     uuid.UUID       `json:"account_id"`
	SignalBatchID uuid.UUID       `json:"signal_batch_id"`
	SignalID      uuid.UUID       `json:"signal_id"`
	VersionNumber int32           `json:"version_number"`
	Content       json.RawMessage `json:"content"`
}

type SignalVersionsP2 struct {
	ID            uuid.UUID       `json:"id"`
	CreatedAt     time.Time       `json:"created_at"`
	AccountID     uuid.UUID       `json:"account_id"`
	SignalBatchID uuid.UUID       `json:"signal_batch_id"`
	SignalID      uuid.UUID       `json:"signal_id"`
	VersionNumber int32           `json:"version_number"`
	Content       json.RawMessage `json:"content"`
}

type SignalVersionsP3 struct {
	ID            uuid.UUID       `json:"id"`
	CreatedAt     time.Time       `json:"created_at"`
	AccountID     uuid.UUID       `json:"account_id"`
	SignalBatchID uuid.UUID       `json:"signal_batch_id"`
	SignalID      uuid.UUID       `json:"signal_id"`
	VersionNumber int32           `json:"version_number"`
	Content       json.RawMessage `json:"content"`
}

type SignalVersionsP4 struct {
	ID            uuid.UUID       `json:"id"`
	CreatedAt     time.Time       `json:"created_at"`
	AccountID     uuid.UUID       `json:"account_id"`
	SignalBatchID uuid.UUID       `json:"signal_batch_id"`
	SignalID      uuid.UUID       `json:"signal_id"`
	VersionNumber int32           `json:"version_number"`
	Content       json.RawMessage `json:"content"`
}

type SignalVersionsP5 struct {
	ID            uuid.UUID       `json:"id"`
	CreatedAt     time.Time       `json:"created_at"`
	AccountID     uuid.UUID       `json:"account_id"`
	SignalBatchID uuid.UUID       `json:"signal_batch_id"`
	SignalID      uuid.UUID       `json:"signal_id"`
	VersionNumber int32           `json:"version_number"`
	Content       json.RawMessage `json:"content"`
}

type SignalVersionsP6 struct {
	ID            uuid.UUID       `json:"id"`
	CreatedAt     time.Time       `json:"created_at"`
	AccountID     uuid.UUID       `json:"account_id"`
	SignalBatchID uuid.UUID       `json:"signal_batch_id"`
	SignalID      uuid.UUID       `json:"signal_id"`
	VersionNumber int32           `json:"version_number"`
	Content       json.RawMessage `json:"content"`
}

type SignalVersionsP7 struct {
	ID            uuid.UUID       `json:"id"`
	CreatedAt     time.Time       `json:"created_at"`
	AccountID     uuid.UUID       `json:"account_id"`
	SignalBatchID uuid.UUID       `json:"signal_batch_id"`
	SignalID      uuid.UUID       `json:"signal_id"`
	VersionNumber int32           `json:"version_number"`
	Content       json.RawMessage `json:"content"`
}

type SignalsP0 struct {
	ID            uuid.UUID `json:"id"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	AccountID     uuid.UUID `json:"account_id"`
	IsnID         uuid.UUID `json:"isn_id"`
	SignalTypeID  uuid.UUID `json:"signal_type_id"`
	LocalRef      string    `json:"local_ref"`
	CorrelationID uuid.UUID `json:"correlation_id"`
	IsWithdrawn   bool      `json:"is_withdrawn"`
	IsArchived    bool      `json:"is_archived"`
}

type SignalsP1 struct {
	ID            uuid.UUID `json:"id"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	AccountID     uuid.UUID `json:"account_id"`
	IsnID         uuid.UUID `json:"isn_id"`
	SignalTypeID  uuid.UUID `json:"signal_type_id"`
	LocalRef      string    `json:"local_ref"`
	CorrelationID uuid.UUID `json:"correlation_id"`
	IsWithdrawn   bool      `json:"is_withdrawn"`
	IsArchived    bool      `json:"is_archived"`
}

type SignalsP2 struct {
	ID            uuid.UUID `json:"id"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	AccountID     uuid.UUID `json:"account_id"`
	IsnID         uuid.UUID `json:"isn_id"`
	SignalTypeID  uuid.UUID `json:"signal_type_id"`
	LocalRef      string    `json:"local_ref"`
	CorrelationID uuid.UUID `json:"correlation_id"`
	IsWithdrawn   bool      `json:"is_withdrawn"`
	IsArchived    bool      `json:"is_archived"`
}

type SignalsP3 struct {
	ID            uuid.UUID `json:"id"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	AccountID     uuid.UUID `json:"account_id"`
	IsnID         uuid.UUID `json:"isn_id"`
	SignalTypeID  uuid.UUID `json:"signal_type_id"`
	LocalRef      string    `json:"local_ref"`
	CorrelationID uuid.UUID `json:"correlation_id"`
	IsWithdrawn   bool      `json:"is_withdrawn"`
	IsArchived    bool      `json:"is_archived"`
}

type SignalsP4 struct {
	ID            uuid.UUID `json:"id"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	AccountID     uuid.UUID `json:"account_id"`
	IsnID         uuid.UUID `json:"isn_id"`
	SignalTypeID  uuid.UUID `json:"signal_type_id"`
	LocalRef      string    `json:"local_ref"`
	CorrelationID uuid.UUID `json:"correlation_id"`
	IsWithdrawn   bool      `json:"is_withdrawn"`
	IsArchived    bool      `json:"is_archived"`
}

type SignalsP5 struct {
	ID            uuid.UUID `json:"id"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	AccountID     uuid.UUID `json:"account_id"`
	IsnID         uuid.UUID `json:"isn_id"`
	SignalTypeID  uuid.UUID `json:"signal_type_id"`
	LocalRef      string    `json:"local_ref"`
	CorrelationID uuid.UUID `json:"correlation_id"`
	IsWithdrawn   bool      `json:"is_withdrawn"`
	IsArchived    bool      `json:"is_archived"`
}

type SignalsP6 struct {
	ID            uuid.UUID `json:"id"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	AccountID     uuid.UUID `json:"account_id"`
	IsnID         uuid.UUID `json:"isn_id"`
	SignalTypeID  uuid.UUID `json:"signal_type_id"`
	LocalRef      string    `json:"local_ref"`
	CorrelationID uuid.UUID `json:"correlation_id"`
	IsWithdrawn   bool      `json:"is_withdrawn"`
	IsArchived    bool      `json:"is_archived"`
}

type SignalsP7 struct {
	ID            uuid.UUID `json:"id"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	AccountID     uuid.UUID `json:"account_id"`
	IsnID         uuid.UUID `json:"isn_id"`
	SignalTypeID  uuid.UUID `json:"signal_type_id"`
	LocalRef      string    `json:"local_ref"`
	CorrelationID uuid.UUID `json:"correlation_id"`
	IsWithdrawn   bool      `json:"is_withdrawn"`
	IsArchived    bool      `json:"is_archived"`
}

type User struct {
	AccountID      uuid.UUID `json:"account_id"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
	Email          string    `json:"email"`
	HashedPassword string    `json:"hashed_password"`
	UserRole       string    `json:"user_role"`
}
